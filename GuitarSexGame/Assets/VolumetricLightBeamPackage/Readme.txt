Hi and thank you for using the Volumetric Light Beam plugin for Unity!
You can find here some resources to help your first steps.

*********************
** How to install? **
*********************
Simply open the 'Installer' asset in the "Assets/VolumetricLightBeamPackage/" folder, and click on the 'Install' button in the inspector.
We highly recommend to restart Unity after installing the new version, otherwise you can encounter script or shader errors.

If you want to install the plugin manually, please follow these steps:
- If you have already a previous version of the VolumetricLightBeam plugin installed in your project:
  - Open a new empty scene
  - Delete the old plugin version (delete the folder "Assets/Plugins/VolumetricLightBeam/" or "Assets/VolumetricLightBeam/" depending on your version)
- Double-click on the "VolumetricLightBeam.unitypackage" file you've just imported to install the plugin
- Read the "Configure the plugin" section


**************************
** Configure the plugin **
**************************
- For the Built-in Render Pipeline: nothing to do!
- For the Universal Render Pipeline (URP): http://saladgamer.com/vlb-doc/render-pipelines/#universal-render-pipeline-urp
- For the High Definition Render Pipeline (HDRP): http://saladgamer.com/vlb-doc/render-pipelines/#high-definition-render-pipeline-hdrp


*******************
** Documentation **
*******************
Please visit: http://saladgamer.com/vlb-doc/

And that's it! You can start to create new volumetric lights!

Thank you very much, and do not hesitate to contact the team for any questions or suggestions: <EMAIL>

Cheers!
