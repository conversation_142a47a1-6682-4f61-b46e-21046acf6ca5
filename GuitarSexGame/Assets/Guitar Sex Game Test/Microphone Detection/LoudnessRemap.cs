using System;
using UnityEngine;

public class RemappedLoudnessEvent
{
    public float LoudnessT;
    public GuitarLoudnessLevel LoudnessLevel;

    public RemappedLoudnessEvent(float loudnessT, GuitarLoudnessLevel loudnessLevel)
    {
        LoudnessT = loudnessT;
        LoudnessLevel = loudnessLevel;
    }
}

public enum GuitarLoudnessLevel
{
    Low, Medium, High
}
public class LoudnessRemap : MonoBehaviour
{
    [Header("Guitar Loudness Level")]
    [Range(0, 1)] public float lowLoudness;
    [Range(0, 1)] public float mediumLoudness;
    [Range(0, 1)] public float highLoudness;
    
    [Header("Guitar Loudness Remap")]
    public float loudnessAt0;
    public float loudnessAt1;
    [SerializeField] private float _lerpedLoudness;
    
    [Header("Detection")]
    public AudioLoudnessDetection detector;
    public float loudnessSensibility = 100;
    public float loudnessThreshold = 0.1f;
    public float loudnessDetectionGapWindow = 0.1f;

    private float _loudnessDetectionTimer;

    public GuitarLoudnessLevel currentLoudnessLevel;

    private void Start()
    {
        currentLoudnessLevel = GuitarLoudnessLevel.Low;
    }

    private void Update()   
    {
        if (_loudnessDetectionTimer > 0)
        {
            _loudnessDetectionTimer -= Time.deltaTime;
        }
        else
        {
            _loudnessDetectionTimer = loudnessDetectionGapWindow;
            
            float loudness = detector.GetLoudnessFromMicrophone();
            loudness *= loudnessSensibility;

            if (loudness < loudnessThreshold)
            {
                loudness = 0;
            }

            if (loudness != 0)
            {
                //Debug.Log(loudness);
            }
        
            _lerpedLoudness = RemapTo01(loudness, loudnessAt0, loudnessAt1);
            
            if (_lerpedLoudness < lowLoudness)
            {
                if (currentLoudnessLevel != GuitarLoudnessLevel.Low)
                {
                    EventBetter.Raise(new RemappedLoudnessEvent(_lerpedLoudness, GuitarLoudnessLevel.Low));
                }
                currentLoudnessLevel = GuitarLoudnessLevel.Low;
            }
            else if (_lerpedLoudness >= lowLoudness && _lerpedLoudness < mediumLoudness)
            {
                if (currentLoudnessLevel != GuitarLoudnessLevel.Medium)
                {
                    EventBetter.Raise(new RemappedLoudnessEvent(_lerpedLoudness, GuitarLoudnessLevel.Medium));
                }
                currentLoudnessLevel = GuitarLoudnessLevel.Medium;
            }
            else if (_lerpedLoudness >= mediumLoudness)
            {
                if (currentLoudnessLevel != GuitarLoudnessLevel.High)
                {
                    EventBetter.Raise(new RemappedLoudnessEvent(_lerpedLoudness, GuitarLoudnessLevel.High));
                }
                currentLoudnessLevel = GuitarLoudnessLevel.High;
            }
        
            DebugOnScreen.Set("Guitar Lerped Loudness: ", _lerpedLoudness.ToString("F2"));
            DebugOnScreen.Set("Guitar Loudness Level: ", currentLoudnessLevel.ToString());
        }
    }
    
    float RemapTo01(float value, float low, float high)
    {
        return Mathf.Clamp01((value - low) / (high - low));
    }

}
