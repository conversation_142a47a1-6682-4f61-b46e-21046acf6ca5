using System;
using UnityEngine;
using UnityEngine.Serialization;

public enum MMInputs
{
    ButtonUp,
    ButtonDown,
    ButtonLeft,
    ButtonRight
}

public class MakeyMakeyInputs : MonoBehaviour
{
    public KeyCode buttonUp;
    public KeyCode buttonDown;
    public KeyCode buttonLeft;
    public KeyCode buttonRight;

    private void Update()
    {
        if (Input.GetKeyDown(buttonUp))
        {
            EventBetter.Raise(MMInputs.ButtonUp);
        }

        if (Input.GetKeyDown(buttonDown))
        {
            EventBetter.Raise(MMInputs.ButtonDown);
        }

        if (Input.GetKeyDown(buttonLeft))
        {
            EventBetter.Raise(MMInputs.ButtonLeft);
        }

        if (Input.GetKeyDown(buttonRight))
        {
            EventBetter.Raise(MMInputs.ButtonRight);
        }
    }
}