using System;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.Serialization;

public class SensitivityLevelEvent
{
    public SensitivityLevel SensitivityLevel;
    public float SensitivityLevelT;
    public SensitivityLevelEvent(SensitivityLevel sensitivityLevel, float sensitivityLevelT)
    {
        SensitivityLevel = sensitivityLevel;
        SensitivityLevelT = sensitivityLevelT;
    }
}
public enum SensitivityLevel
{
    LowSensitivity,
    PerfectSensitivity,
    Climax,
    Tired
}
public class SensitivityLevelChangeEvent
{
    public SensitivityLevel CurrentSensitivityLevel;

    public SensitivityLevelChangeEvent(SensitivityLevel sensitivityLevel)
    {
        CurrentSensitivityLevel = sensitivityLevel;
    }
}

public class SensitivityChangeEvent
{
    public float ChangeAmount;

    public SensitivityChangeEvent(float changeAmount)
    {
        ChangeAmount = changeAmount;
    }
}
public class SensitivityMeter : MonoBehaviour
{
    public SensitivityLevel currentSensitivityLevel;
    // if lower than this, an itchy signal will be evoked
    public float lowSensitivityThreshold = 30f;
    public float perfectSensitivityThreshold = 60f;
    public float climaxSensitivityThreshold = 70f;
    
    public float meterCapacity = 100f;
    public float decreaseSpeed = 1f;

    [Header("Guitar Sensitivity Change Amount At Sensitivity Level Low")]
    public float lowLoudnessAtLowSensitivityChangeAmount;
    public float mediumLoudnessAtLowSensitivityChangeAmount;
    public float highLoudnessAtLowSensitivityChangeAmount;
    public float mmInputAtLowSensitivityChangeAmount;
    [Header("Guitar Sensitivity Change Amount At Sensitivity Level Perfect")]
    public float lowLoudnessAtPerfectSensitivityChangeAmount;
    public float mediumLoudnessAtPerfectSensitivityChangeAmount;
    public float highLoudnessAtPerfectSensitivityChangeAmount;
    public float mmInputAtPerfectSensitivityChangeAmount;
    [Header("Guitar Sensitivity Change Amount At Sensitivity Level Climax")]
    public float lowLoudnessAtClimaxSensitivityChangeAmount;
    public float mediumLoudnessAtClimaxSensitivityChangeAmount;
    public float highLoudnessAtClimaxSensitivityChangeAmount;
    public float mmInputAtClimaxSensitivityChangeAmount;
    [Header("Guitar Sensitivity Change Amount At Sensitivity Level Tired")]
    public float lowLoudnessAtTiredSensitivityChangeAmount;
    public float mediumLoudnessAtTiredSensitivityChangeAmount;
    public float highLoudnessAtTiredSensitivityChangeAmount;
    public float mmInputAtTiredSensitivityChangeAmount;
    
    public float CurrentSensitivity
    {
        get => _currentSensitivity;
    }
    [SerializeField] private float _currentSensitivity;

    private void Awake()
    {
        currentSensitivityLevel = SensitivityLevel.LowSensitivity;

        //_currentSensitivity = 0f;
        EventBetter.Listen(this, (SensitivityChangeEvent gse) =>
        {
            _currentSensitivity += gse.ChangeAmount;
        });
        
        EventBetter.Listen(this, (MMInputs mmInputs) =>
        {
            switch (currentSensitivityLevel)
            {
                case SensitivityLevel.LowSensitivity:
                    _currentSensitivity += mmInputAtLowSensitivityChangeAmount;
                    break;
                case SensitivityLevel.PerfectSensitivity:
                    _currentSensitivity += mmInputAtPerfectSensitivityChangeAmount;
                    break;
                case SensitivityLevel.Climax:
                    _currentSensitivity += mmInputAtClimaxSensitivityChangeAmount;
                    break;
                case SensitivityLevel.Tired:
                    _currentSensitivity += mmInputAtTiredSensitivityChangeAmount;
                    break;
            }
        });
        
        EventBetter.Listen(this, (RemappedLoudnessEvent rle) =>
        {
            switch (currentSensitivityLevel)
            {
                case SensitivityLevel.LowSensitivity:
                    switch (rle.LoudnessLevel)
                    {
                        case GuitarLoudnessLevel.Low:
                            _currentSensitivity += lowLoudnessAtLowSensitivityChangeAmount ;
                            break;
                        case GuitarLoudnessLevel.Medium:
                            _currentSensitivity += mediumLoudnessAtLowSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.High:
                            _currentSensitivity += highLoudnessAtLowSensitivityChangeAmount;
                            break;
                    }
                    break;
                case SensitivityLevel.PerfectSensitivity:
                    switch (rle.LoudnessLevel)
                    {
                        case GuitarLoudnessLevel.Low:
                            _currentSensitivity += lowLoudnessAtPerfectSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.Medium:
                            _currentSensitivity += mediumLoudnessAtPerfectSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.High:
                            _currentSensitivity += highLoudnessAtPerfectSensitivityChangeAmount;
                            break;
                    }
                    break;
                case SensitivityLevel.Climax:
                    switch (rle.LoudnessLevel)
                    {
                        case GuitarLoudnessLevel.Low:
                            _currentSensitivity += lowLoudnessAtClimaxSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.Medium:
                            _currentSensitivity += mediumLoudnessAtClimaxSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.High:
                            _currentSensitivity += highLoudnessAtClimaxSensitivityChangeAmount;
                            break;
                    }
                    break;
                case SensitivityLevel.Tired:
                    switch (rle.LoudnessLevel)
                    {
                        case GuitarLoudnessLevel.Low:
                            _currentSensitivity += lowLoudnessAtTiredSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.Medium:
                            _currentSensitivity += mediumLoudnessAtTiredSensitivityChangeAmount;
                            break;
                        case GuitarLoudnessLevel.High:
                            _currentSensitivity += highLoudnessAtTiredSensitivityChangeAmount;
                            break;
                    }
                    break;
            }
        });
    }

    private void Start()
    {
        EventBetter.Raise(new SensitivityLevelChangeEvent(SensitivityLevel.LowSensitivity));
    }

    private void Update()
    {
        if (_currentSensitivity > meterCapacity)
        {
            _currentSensitivity = meterCapacity;
        }
        if (_currentSensitivity > 0)
        {
            _currentSensitivity -= decreaseSpeed * Time.deltaTime;
        }

        if (_currentSensitivity < lowSensitivityThreshold)
        {
            if (currentSensitivityLevel != SensitivityLevel.LowSensitivity)
            {
                EventBetter.Raise(new SensitivityLevelChangeEvent(SensitivityLevel.LowSensitivity));
            }
            currentSensitivityLevel = SensitivityLevel.LowSensitivity;
            EventBetter.Raise(SensitivityLevel.LowSensitivity);
        }
        else if(_currentSensitivity >= lowSensitivityThreshold && _currentSensitivity < perfectSensitivityThreshold)
        {
            if (currentSensitivityLevel != SensitivityLevel.PerfectSensitivity)
            {
                EventBetter.Raise(new SensitivityLevelChangeEvent(SensitivityLevel.PerfectSensitivity));
            }
            currentSensitivityLevel = SensitivityLevel.PerfectSensitivity;
            EventBetter.Raise(SensitivityLevel.PerfectSensitivity);
        }
        else if (_currentSensitivity >= perfectSensitivityThreshold && _currentSensitivity < climaxSensitivityThreshold)
        {
            if (currentSensitivityLevel != SensitivityLevel.Climax)
            {
                EventBetter.Raise(new SensitivityLevelChangeEvent(SensitivityLevel.Climax));
            }
            currentSensitivityLevel = SensitivityLevel.Climax;
            EventBetter.Raise(SensitivityLevel.Climax);
        }
        else if (_currentSensitivity >= climaxSensitivityThreshold)
        {
            if (currentSensitivityLevel != SensitivityLevel.Tired)
            {
                EventBetter.Raise(new SensitivityLevelChangeEvent(SensitivityLevel.Tired));
            }
            currentSensitivityLevel = SensitivityLevel.Tired;
            EventBetter.Raise(SensitivityLevel.Tired);
        }

        EventBetter.Raise(new SensitivityLevelEvent(currentSensitivityLevel, _currentSensitivity/meterCapacity));
        
        DebugOnScreen.Set("Sensitivity Meter:", currentSensitivityLevel.ToString());
        DebugOnScreen.Set("Sensitivity Level:", _currentSensitivity.ToString());

    }
}
