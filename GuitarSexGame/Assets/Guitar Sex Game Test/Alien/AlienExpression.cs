using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.Serialization;
using Debug = UnityEngine.Debug;

public class AlienJumpEvent
{
}

public class AlienPoseChangeEvent
{
}

public class ShakeCameraEvent
{
    public float Duration;
    public float Intensity;

    public ShakeCameraEvent(float duration, float intensity)
    {
        Duration = duration;
        Intensity = intensity;
    }
}

public class BackgroundBloomEvent
{
    public float BloomTargetIntensity;
    public float BloomDuration;

    public BackgroundBloomEvent(float bloomTargetIntensity, float bloomDuration)
    {
        BloomTargetIntensity = bloomTargetIntensity;
        BloomDuration = bloomDuration;
    }
}

public class SpotlightHueEvent
{
    public Color HueColor;

    public SpotlightHueEvent(Color hueColor)
    {
        HueColor = hueColor;
    }
}

public class FlickerEvent
{
    public float FlickerInterval;
    public FlickerType CurrentFlickerType;

    public FlickerEvent(float flickerInterval, FlickerType flickerType)
    {
        FlickerInterval = flickerInterval;
        CurrentFlickerType = flickerType;
    }
}

public class ColorAdjustmentEvent
{
    public Color AdjustmentColor;
    public float AdjustmentDuration;

    public ColorAdjustmentEvent(Color adjustmentColor, float adjustmentDuration)
    {
        AdjustmentColor = adjustmentColor;
        AdjustmentDuration = adjustmentDuration;
    }
}

public class StopFlickerEvent
{
}

public class AlienExpression : MonoBehaviour
{
    // [Header("Spotlight Settings")] 
    // public Color spotlightColorLowLoudness;
    // public Color spotlightColorMediumLoudness;
    // public Color spotlightColorHighLoudness;
    [Header("Color Adjustment Settings")] public Color colorAtLowSensitivity;
    public float colorAdjustmentDurationAtLowSensitivity;
    public Color colorAtPerfectSensitivity;
    public float colorAdjustmentDurationAtPerfectSensitivity;
    public Color colorAtClimaxSensitivity;
    public float colorAdjustmentDurationAtClimaxSensitivity;
    public Color colorAtTiredSensitivity;
    public float colorAdjustmentDurationAtTiredSensitivity;
    [Header("Camera Shake")] public float cameraBaseIntensity;
    public float cameraBaseDuration;
    public AnimationCurve cameraCurve;
    public float animationGuitarDuration = 0.5f;
    [Header("Background Bloom")] public float bloomIntensity;
    public float bloomDuration;
    public SensitivityMeter sensitivityMeter;

    [FormerlySerializedAs("flickerDurationPerfect")] [Header("Flicker Settings")]
    public float flickerIntervalPerfect;

    public FlickerType flickerTypeAtPerfect;
    public float flickerIntervalClimax;
    public FlickerType flickerTypeClimax;
    public float flickerIntervalTired;
    public FlickerType flickerTypeAtTired;

    [SerializeField, Space(10)] private SensitivityLevel _sensitivityLevel;

    private void Awake()
    {
        // Listen to sensitivity level
        EventBetter.Listen(this, (SensitivityLevel sl) => { _sensitivityLevel = sl; });

        // Set Effects when current sensitivity level is changed
        EventBetter.Listen(this, (SensitivityLevelChangeEvent slc) =>
        {
            switch (slc.CurrentSensitivityLevel)
            {
                case SensitivityLevel.LowSensitivity:
                    EventBetter.Raise(new FlickerEvent(flickerIntervalPerfect, FlickerType.SimpleOnOff));
                    EventBetter.Raise(new ColorAdjustmentEvent(colorAtLowSensitivity,
                        colorAdjustmentDurationAtLowSensitivity));
                    break;
                case SensitivityLevel.PerfectSensitivity:
                    EventBetter.Raise(new FlickerEvent(flickerIntervalPerfect, flickerTypeAtPerfect));
                    EventBetter.Raise(new ColorAdjustmentEvent(colorAtPerfectSensitivity,
                        colorAdjustmentDurationAtPerfectSensitivity));
                    break;
                case SensitivityLevel.Climax:
                    EventBetter.Raise(new FlickerEvent(flickerIntervalClimax, flickerTypeClimax));
                    EventBetter.Raise(new ColorAdjustmentEvent(colorAtClimaxSensitivity,
                        colorAdjustmentDurationAtClimaxSensitivity));
                    break;
                case SensitivityLevel.Tired:
                    EventBetter.Raise(new FlickerEvent(flickerIntervalTired, flickerTypeAtTired));
                    EventBetter.Raise(new ColorAdjustmentEvent(colorAtTiredSensitivity,
                        colorAdjustmentDurationAtTiredSensitivity));
                    break;
            }
        });

        EventBetter.Listen(this, (MMInputs mmi) =>
        {
            float animationCurveValue = cameraCurve.Evaluate(sensitivityMeter.CurrentSensitivity /
                                                             sensitivityMeter.meterCapacity);

            Debug.Log(animationCurveValue);

            EventBetter.Raise(new AlienJumpEvent());
            EventBetter.Raise(new AlienPoseChangeEvent());
            EventBetter.Raise(new ShakeCameraEvent(
                animationCurveValue * cameraBaseDuration, animationCurveValue * cameraBaseIntensity));
            EventBetter.Raise(new BackgroundBloomEvent(bloomIntensity * animationCurveValue, bloomDuration));
            // switch (mmi)
            // {
            //     case MMInputs.ButtonUp:
            //         EventBetter.Raise(new AlienJumpEvent());
            //         EventBetter.Raise(new AlienPoseChangeEvent());
            //         EventBetter.Raise(new ShakeCameraEvent(
            //             animationCurveValue * cameraBaseDuration, animationCurveValue * cameraBaseIntensity));
            //         break;
            //     case MMInputs.ButtonDown:
            //         EventBetter.Raise(new AlienJumpEvent());
            //         EventBetter.Raise(new AlienPoseChangeEvent());
            //         EventBetter.Raise(new ShakeCameraEvent(
            //             animationCurveValue * cameraBaseDuration, animationCurveValue * cameraBaseIntensity));
            //         break;
            //         break;
            //     case MMInputs.ButtonLeft:
            //         EventBetter.Raise(new AlienJumpEvent());
            //         EventBetter.Raise(new AlienPoseChangeEvent());
            //         EventBetter.Raise(new ShakeCameraEvent(
            //             animationCurveValue * cameraBaseDuration, animationCurveValue * cameraBaseIntensity));
            //         break;
            //     case MMInputs.ButtonRight:
            //         EventBetter.Raise(new AlienJumpEvent());
            //         EventBetter.Raise(new AlienPoseChangeEvent());
            //         EventBetter.Raise(new ShakeCameraEvent(
            //             animationCurveValue * cameraBaseDuration, animationCurveValue * cameraBaseIntensity));
            //         break;
            // }
        });

        EventBetter.Listen(this, (RemappedLoudnessEvent rle) =>
        {
            float animationCurveValue = cameraCurve.Evaluate(sensitivityMeter.CurrentSensitivity /
                                                             sensitivityMeter.meterCapacity);
            EventBetter.Raise(new ShakeCameraEvent(
                animationCurveValue * animationGuitarDuration * rle.LoudnessT,
                animationCurveValue * cameraBaseIntensity * rle.LoudnessT));
        });

        // EventBetter.Listen(this,
        //     (RemappedLoudnessEvent rle) =>
        //     {
        //         Color hueColor = Color.clear;
        //         switch (rle.LoudnessLevel)
        //         {
        //             case GuitarLoudnessLevel.Low:
        //                 hueColor = spotlightColorLowLoudness;
        //                 break;
        //             case GuitarLoudnessLevel.Medium:
        //                 hueColor = spotlightColorMediumLoudness;
        //                 break;
        //             case GuitarLoudnessLevel.High:
        //                 hueColor = spotlightColorHighLoudness;
        //                 break;
        //         }
        //         
        //         //EventBetter.Raise(new SpotlightHueEvent(hueColor));
        //     });
    }
}