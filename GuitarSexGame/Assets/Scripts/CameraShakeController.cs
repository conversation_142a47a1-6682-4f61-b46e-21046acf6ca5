using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

public class CameraShakeController : MonoBehaviour
{
    public float shakeIntensity = 0.5f; // Public variable to control shake intensity
    public bool triggerShake = false;
    private bool isShaking = false;

    private void Awake()
    {
        EventBetter.Listen(this, (ShakeCameraEvent sce) =>
        {
            ShakeCamera(sce.Duration);
            shakeIntensity = sce.Intensity;
        });
    }

    // Start is called before the first frame update
    void Start()
    {
    }

    // Update is called once per frame
    void Update()
    {
        if (triggerShake)
        {
            ShakeCamera(.2f);
            triggerShake = false; // Turn off the trigger after starting the shake
        }
    }

    public void ShakeCamera(float duration)
    {
        if (!isShaking)
        {
            StartCoroutine(PerformShake(duration));
        }
    }

    private IEnumerator PerformShake(float duration)
    {
        isShaking = true;
        Vector3 originalLocalPosition = transform.localPosition;
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            float offsetX = Random.Range(-1f, 1f) * shakeIntensity;
            float offsetY = Random.Range(-1f, 1f) * shakeIntensity;
            float offsetZ = Random.Range(-1f, 1f) * shakeIntensity;

            transform.localPosition = originalLocalPosition + new Vector3(offsetX, offsetY, offsetZ);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        transform.localPosition = originalLocalPosition;
        isShaking = false;
    }
}