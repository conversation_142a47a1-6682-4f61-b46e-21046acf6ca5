using System;
using System.Collections;
using UnityEngine;
using Random = UnityEngine.Random;

public class BodyController : MonoBehaviour
{
    public int activePattern = 0; // Index to set the current active pattern (0 = none, 1 = random shake, etc.)
    public bool activateRandomPattern = false; // Bool to activate random pattern selection
    
    // Pattern 1: Random Shake
    public float shakeFrequency = 10f; // Frequency of random shake
    public float shakeMagnitude = 0.5f; // Magnitude of random shake

    // Pattern 2, 3, 4: Sine Wave Movement
    public float sineAmplitude = 1f; // Amplitude of sine wave
    public float sineFrequency = 1f; // Frequency of sine wave

    // Pattern 5: Circular Movement
    public float circleRadius = 1f; // Radius of circular movement
    public float circleSpeed = 1f; // Speed of circular movement

    private Vector3 originalPosition; // Original position of the object

    private SensitivityLevel _currentSensitivityLevel;

    private void Awake()
    {
        EventBetter.Listen(this, (SensitivityLevel sl) =>
        {
            _currentSensitivityLevel = sl;
        });
        EventBetter.Listen(this, (AlienPoseChangeEvent ape) =>
        {
            if (_currentSensitivityLevel == SensitivityLevel.Climax)
            {
                SetPattern(0);
            }
            else
            {
                SetRandomPattern();
            }
        });
    }

    void Start()
    {
        // Store the original position of the object
        originalPosition = transform.position;
    }

    void Update()
    {
        // Check if random pattern activation is triggered
        if (activateRandomPattern)
        {
            SetRandomPattern();
            activateRandomPattern = false; // Reset the trigger
        }

        // Reset to the original position before applying any pattern
        transform.position = originalPosition;

        // Apply the active pattern
        switch (activePattern)
        {
            case 1:
                ApplyRandomShake();
                break;
            case 2:
                ApplySineWaveXZ();
                break;
            case 3:
                ApplySineWaveXY();
                break;
            case 4:
                ApplySineWaveYZ();
                break;
            case 5:
                ApplyCircularMovementXZ();
                break;
        }
    }

    public void SetPattern(int patternIndex)
    {
        // Set the active pattern and reset the position to the original position
        activePattern = patternIndex;
        transform.position = originalPosition; // Reset position
    }

    public void SetRandomPattern()
    {
        // Set a random pattern index (excluding 0, which is "none")
        int randomPattern = Random.Range(2, 6); // Patterns 1 to 5
        SetPattern(randomPattern);
    }

    private void ApplyRandomShake()
    {
        float offsetX = Random.Range(-1f, 1f) * shakeMagnitude;
        float offsetY = Random.Range(-1f, 1f) * shakeMagnitude;
        float offsetZ = Random.Range(-1f, 1f) * shakeMagnitude;

        transform.position += new Vector3(offsetX, offsetY, offsetZ);
    }

    private void ApplySineWaveXZ()
    {
        float offsetX = Mathf.Sin(Time.time * sineFrequency) * sineAmplitude;
        float offsetZ = Mathf.Cos(Time.time * sineFrequency) * sineAmplitude;

        transform.position += new Vector3(offsetX, 0, offsetZ);
    }

    private void ApplySineWaveXY()
    {
        float offsetX = Mathf.Sin(Time.time * sineFrequency) * sineAmplitude;
        float offsetY = Mathf.Cos(Time.time * sineFrequency) * sineAmplitude;

        transform.position += new Vector3(offsetX, offsetY, 0);
    }

    private void ApplySineWaveYZ()
    {
        float offsetY = Mathf.Sin(Time.time * sineFrequency) * sineAmplitude;
        float offsetZ = Mathf.Cos(Time.time * sineFrequency) * sineAmplitude;

        transform.position += new Vector3(0, offsetY, offsetZ);
    }

    private void ApplyCircularMovementXZ()
    {
        float offsetX = Mathf.Cos(Time.time * circleSpeed) * circleRadius;
        float offsetZ = Mathf.Sin(Time.time * circleSpeed) * circleRadius;

        transform.position += new Vector3(offsetX, 0, offsetZ);
    }
}
