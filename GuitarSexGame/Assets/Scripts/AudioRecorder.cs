using System;
using System.IO;
using UnityEngine;

/// <summary>
/// 专门处理录音逻辑和文件保存的类
/// </summary>
public class AudioRecorder : MonoBehaviour
{
    [Header("Recording Settings")]
    [SerializeField] private int recordingLength = 60; // 最大录音时长（秒）
    [SerializeField] private int sampleRate = 44100; // 采样率
    
    [Header("Audio Components")]
    [SerializeField] private AudioSource audioSource;
    
    // 录音状态
    public bool IsRecording { get; private set; }
    public bool HasRecording { get; private set; }
    
    // 录音数据
    private AudioClip recordedClip;
    private string microphoneName;
    private bool microphoneConnected;
    
    // 事件
    public event Action OnRecordingStarted;
    public event Action OnRecordingStopped;
    public event Action<string> OnRecordingSaved;
    public event Action<string> OnError;
    
    private void Start()
    {
        InitializeMicrophone();
        
        // 如果没有指定AudioSource，尝试获取或创建一个
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
    }
    
    /// <summary>
    /// 初始化麦克风
    /// </summary>
    private void InitializeMicrophone()
    {
        if (Microphone.devices.Length <= 0)
        {
            microphoneConnected = false;
            OnError?.Invoke("No microphone device detected!");
            Debug.LogWarning("Microphone not connected!");
            return;
        }
        
        microphoneConnected = true;
        microphoneName = Microphone.devices[0];
        
        // 获取麦克风支持的频率范围
        int minFreq, maxFreq;
        Microphone.GetDeviceCaps(microphoneName, out minFreq, out maxFreq);
        
        // 如果支持任意频率，使用44100Hz
        if (minFreq == 0 && maxFreq == 0)
        {
            sampleRate = 44100;
        }
        else
        {
            sampleRate = Mathf.Clamp(sampleRate, minFreq, maxFreq);
        }
        
        Debug.Log($"Microphone initialized: {microphoneName}, Sample rate: {sampleRate}Hz");
    }
    
    /// <summary>
    /// 开始录音
    /// </summary>
    public void StartRecording()
    {
        if (!microphoneConnected)
        {
            OnError?.Invoke("Microphone not connected!");
            return;
        }

        if (IsRecording)
        {
            OnError?.Invoke("Already recording!");
            return;
        }
        
        try
        {
            recordedClip = Microphone.Start(microphoneName, false, recordingLength, sampleRate);
            IsRecording = true;
            HasRecording = false;
            
            OnRecordingStarted?.Invoke();
            Debug.Log("Recording started...");
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Failed to start recording: {e.Message}");
            Debug.LogError($"Failed to start recording: {e.Message}");
        }
    }
    
    /// <summary>
    /// 停止录音
    /// </summary>
    public void StopRecording()
    {
        if (!IsRecording)
        {
            OnError?.Invoke("Not currently recording!");
            return;
        }

        try
        {
            Microphone.End(microphoneName);
            IsRecording = false;
            HasRecording = true;

            OnRecordingStopped?.Invoke();
            Debug.Log("Recording stopped");
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Failed to stop recording: {e.Message}");
            Debug.LogError($"Failed to stop recording: {e.Message}");
        }
    }
    
    /// <summary>
    /// 播放录制的音频
    /// </summary>
    public void PlayRecording()
    {
        if (!HasRecording || recordedClip == null)
        {
            OnError?.Invoke("No recording to play!");
            return;
        }

        audioSource.clip = recordedClip;
        audioSource.Play();
        Debug.Log("Playing recording...");
    }

    /// <summary>
    /// 停止播放
    /// </summary>
    public void StopPlayback()
    {
        if (audioSource.isPlaying)
        {
            audioSource.Stop();
            Debug.Log("Playback stopped");
        }
    }
    
    /// <summary>
    /// 保存录音为WAV文件
    /// </summary>
    public void SaveRecording()
    {
        if (!HasRecording || recordedClip == null)
        {
            OnError?.Invoke("No recording to save!");
            return;
        }

        try
        {
            string fileName = $"Recording_{DateTime.Now:yyyyMMdd_HHmmss}.wav";
            string filePath = Path.Combine(Application.persistentDataPath, fileName);

            SaveWavFile(filePath, recordedClip);

            OnRecordingSaved?.Invoke(filePath);
            Debug.Log($"Recording saved to: {filePath}");
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Failed to save recording: {e.Message}");
            Debug.LogError($"Failed to save recording: {e.Message}");
        }
    }
    
    /// <summary>
    /// 将AudioClip保存为WAV文件
    /// </summary>
    private void SaveWavFile(string filePath, AudioClip clip)
    {
        float[] samples = new float[clip.samples * clip.channels];
        clip.GetData(samples, 0);
        
        using (FileStream fileStream = new FileStream(filePath, FileMode.Create))
        using (BinaryWriter writer = new BinaryWriter(fileStream))
        {
            // WAV文件头
            writer.Write("RIFF".ToCharArray());
            writer.Write(36 + samples.Length * 2);
            writer.Write("WAVE".ToCharArray());
            
            // fmt chunk
            writer.Write("fmt ".ToCharArray());
            writer.Write(16);
            writer.Write((short)1);
            writer.Write((short)clip.channels);
            writer.Write(clip.frequency);
            writer.Write(clip.frequency * clip.channels * 2);
            writer.Write((short)(clip.channels * 2));
            writer.Write((short)16);
            
            // data chunk
            writer.Write("data".ToCharArray());
            writer.Write(samples.Length * 2);
            
            // 写入音频数据
            foreach (float sample in samples)
            {
                short intSample = (short)(sample * short.MaxValue);
                writer.Write(intSample);
            }
        }
    }
    
    /// <summary>
    /// 获取录音时长
    /// </summary>
    public float GetRecordingTime()
    {
        if (IsRecording && recordedClip != null)
        {
            return (float)Microphone.GetPosition(microphoneName) / sampleRate;
        }
        else if (HasRecording && recordedClip != null)
        {
            return recordedClip.length;
        }
        return 0f;
    }
    
    /// <summary>
    /// 清除当前录音
    /// </summary>
    public void ClearRecording()
    {
        if (IsRecording)
        {
            StopRecording();
        }
        
        if (audioSource.isPlaying)
        {
            StopPlayback();
        }
        
        if (recordedClip != null)
        {
            Destroy(recordedClip);
            recordedClip = null;
        }
        
        HasRecording = false;
        Debug.Log("Recording cleared");
    }
    
    private void OnDestroy()
    {
        if (IsRecording)
        {
            StopRecording();
        }
        
        if (recordedClip != null)
        {
            Destroy(recordedClip);
        }
    }
}
