using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Random = UnityEngine.Random;

public class GlobalVolumeController : MonoBehaviour
{
    public Volume globalVolume; // Reference to the global volume
    public float hueShiftMin = -180f; // Minimum hue shift value
    public float hueShiftMax = 180f; // Maximum hue shift value
    public float lerpSpeed = 2f; // Speed of lerping to the target hue shift
    public float bloomLerpSpeed = 5f;

    private ColorAdjustments colorAdjustments; // Reference to the Color Adjustments override
    private float targetHueShift; // Target hue shift value
    
    private Bloom _bloom;
    private float _defaultBloom;
    private float _targetBloom;
    private float _currentBloomDuration;
    private float _currentBloomDurationTarget;
    
    private Color _targetColorAdjustment;
    private float _targetColorAdjustmentDuration;
    private float _currentColorAdjustmentDuration;
    private float _colorLerpSpeed;

    private void Awake()
    {
        EventBetter.Listen(this, (BackgroundBloomEvent bbe) =>
        {
            _currentBloomDuration = 0f;
            _currentBloomDurationTarget = bbe.BloomDuration;
            _targetBloom = bbe.BloomTargetIntensity;
        });
        
        EventBetter.Listen(this, (ColorAdjustmentEvent cae) =>
        {
            _currentColorAdjustmentDuration = 0f;
            _targetColorAdjustment = cae.AdjustmentColor;
            _targetColorAdjustmentDuration = cae.AdjustmentDuration;
            if (_targetColorAdjustmentDuration != 0)
            {
                _colorLerpSpeed = 1 / _targetColorAdjustmentDuration;
            }
        });
    }

    void Start()
    {
        // Get the Color Adjustments override from the global volume
        if (globalVolume != null && globalVolume.profile.TryGet(out colorAdjustments))
        {
            targetHueShift = colorAdjustments.hueShift.value; // Initialize target hue shift
        }
        else
        {
            Debug.LogError("Color Adjustments override not found in the global volume profile.");
        }
        
        if (globalVolume != null && globalVolume.profile.TryGet(out _bloom))
        {
            _defaultBloom = _bloom.intensity.value;
        }
    }

    void Update()
    {
        // Smoothly lerp the hue shift to the target value
        if (colorAdjustments != null)
        {
            colorAdjustments.hueShift.value = Mathf.Lerp(colorAdjustments.hueShift.value, targetHueShift, Time.deltaTime * lerpSpeed);
        }

        if (_currentBloomDuration < _currentBloomDurationTarget)
        {
            _currentBloomDuration += Time.deltaTime;
            _bloom.intensity.value = _targetBloom;
        }
        else
        {
            _bloom.intensity.value = Mathf.Lerp(_bloom.intensity.value, _defaultBloom, Time.deltaTime * bloomLerpSpeed);
        }

        if (_currentColorAdjustmentDuration < _targetColorAdjustmentDuration)
        {
            _currentColorAdjustmentDuration += Time.deltaTime;
            colorAdjustments.colorFilter.value = Color.Lerp(colorAdjustments.colorFilter.value, _targetColorAdjustment, _currentColorAdjustmentDuration / _targetColorAdjustmentDuration);
        }
        
        if (Input.GetKeyDown(KeyCode.E))
        {
            SetRandomHueShift();
        }
    }

    public void SetRandomHueShift()
    {
        // Randomly set the target hue shift within the specified range
        targetHueShift = Random.Range(hueShiftMin, hueShiftMax);
    }
    
    
}
