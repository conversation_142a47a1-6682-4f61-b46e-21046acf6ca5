using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 录音场景设置助手 - 用于在编辑器中快速创建录音场景UI
/// </summary>
public class RecordSceneSetup : MonoBehaviour
{
    [Header("Scene Setup")]
    [SerializeField] private bool autoSetupOnStart = true;
    
    [Header("UI Prefab Settings")]
    [SerializeField] private Font defaultFont;
    [SerializeField] private Color backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
    [SerializeField] private Color buttonColor = new Color(0.3f, 0.3f, 0.3f, 1f);
    [SerializeField] private Color textColor = Color.white;
    
    private void Start()
    {
        if (autoSetupOnStart)
        {
            SetupRecordScene();
        }
    }
    
    /// <summary>
    /// 设置录音场景
    /// </summary>
    [ContextMenu("Setup Record Scene")]
    public void SetupRecordScene()
    {
        // 创建Canvas
        GameObject canvasGO = CreateCanvas();
        
        // 创建背景
        CreateBackground(canvasGO);
        
        // 创建标题
        CreateTitle(canvasGO);
        
        // 创建按钮区域
        CreateButtonArea(canvasGO);
        
        // 创建状态显示区域
        CreateStatusArea(canvasGO);
        
        // 创建AudioRecorder
        CreateAudioRecorder();
        
        // 设置RecordSceneUI
        SetupRecordSceneUI(canvasGO);
        
        Debug.Log("录音场景设置完成！");
    }
    
    /// <summary>
    /// 创建Canvas
    /// </summary>
    private GameObject CreateCanvas()
    {
        GameObject canvasGO = GameObject.Find("Canvas");
        if (canvasGO == null)
        {
            canvasGO = new GameObject("Canvas");
            Canvas canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        return canvasGO;
    }
    
    /// <summary>
    /// 创建背景
    /// </summary>
    private void CreateBackground(GameObject parent)
    {
        GameObject backgroundGO = new GameObject("Background");
        backgroundGO.transform.SetParent(parent.transform, false);
        
        Image background = backgroundGO.AddComponent<Image>();
        background.color = backgroundColor;
        
        RectTransform rect = backgroundGO.GetComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 创建标题
    /// </summary>
    private void CreateTitle(GameObject parent)
    {
        GameObject titleGO = new GameObject("Title");
        titleGO.transform.SetParent(parent.transform, false);

        // 添加RectTransform组件
        RectTransform rect = titleGO.AddComponent<RectTransform>();

        TextMeshProUGUI title = titleGO.AddComponent<TextMeshProUGUI>();
        title.text = "音频录制器";
        title.fontSize = 36;
        title.color = textColor;
        title.alignment = TextAlignmentOptions.Center;

        rect.anchorMin = new Vector2(0, 0.8f);
        rect.anchorMax = new Vector2(1, 1f);
        rect.offsetMin = Vector2.zero;
        rect.offsetMax = Vector2.zero;
    }
    
    /// <summary>
    /// 创建按钮区域
    /// </summary>
    private void CreateButtonArea(GameObject parent)
    {
        GameObject buttonAreaGO = new GameObject("ButtonArea");
        buttonAreaGO.transform.SetParent(parent.transform, false);

        // 添加RectTransform组件
        RectTransform areaRect = buttonAreaGO.AddComponent<RectTransform>();
        areaRect.anchorMin = new Vector2(0.1f, 0.4f);
        areaRect.anchorMax = new Vector2(0.9f, 0.7f);
        areaRect.offsetMin = Vector2.zero;
        areaRect.offsetMax = Vector2.zero;
        
        // 添加网格布局
        GridLayoutGroup grid = buttonAreaGO.AddComponent<GridLayoutGroup>();
        grid.cellSize = new Vector2(150, 60);
        grid.spacing = new Vector2(20, 20);
        grid.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        grid.constraintCount = 2;
        grid.childAlignment = TextAnchor.MiddleCenter;
        
        // 创建按钮
        CreateButton(buttonAreaGO, "RecordButton", "开始录音");
        CreateButton(buttonAreaGO, "PlayButton", "播放录音");
        CreateButton(buttonAreaGO, "SaveButton", "保存录音");
        CreateButton(buttonAreaGO, "ClearButton", "清除录音");
    }
    
    /// <summary>
    /// 创建按钮
    /// </summary>
    private GameObject CreateButton(GameObject parent, string name, string text)
    {
        GameObject buttonGO = new GameObject(name);
        buttonGO.transform.SetParent(parent.transform, false);

        // 添加RectTransform组件
        RectTransform buttonRect = buttonGO.AddComponent<RectTransform>();

        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = buttonColor;

        Button button = buttonGO.AddComponent<Button>();

        // 创建按钮文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);

        // 添加RectTransform组件到文本对象
        RectTransform textRect = textGO.AddComponent<RectTransform>();

        TextMeshProUGUI buttonText = textGO.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 18;
        buttonText.color = textColor;
        buttonText.alignment = TextAlignmentOptions.Center;

        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        return buttonGO;
    }
    
    /// <summary>
    /// 创建状态显示区域
    /// </summary>
    private void CreateStatusArea(GameObject parent)
    {
        GameObject statusAreaGO = new GameObject("StatusArea");
        statusAreaGO.transform.SetParent(parent.transform, false);

        // 添加RectTransform组件
        RectTransform areaRect = statusAreaGO.AddComponent<RectTransform>();
        areaRect.anchorMin = new Vector2(0.1f, 0.1f);
        areaRect.anchorMax = new Vector2(0.9f, 0.35f);
        areaRect.offsetMin = Vector2.zero;
        areaRect.offsetMax = Vector2.zero;
        
        // 添加垂直布局
        VerticalLayoutGroup layout = statusAreaGO.AddComponent<VerticalLayoutGroup>();
        layout.spacing = 10;
        layout.childAlignment = TextAnchor.MiddleCenter;
        layout.childControlHeight = false;
        layout.childControlWidth = false;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = true;
        
        // 创建状态文本
        CreateStatusText(statusAreaGO, "StatusText", "准备录音", 24);
        CreateStatusText(statusAreaGO, "TimeText", "时长: 00:00", 20);
        CreateStatusText(statusAreaGO, "InstructionText", "点击录音按钮开始录制音频", 16);
    }
    
    /// <summary>
    /// 创建状态文本
    /// </summary>
    private GameObject CreateStatusText(GameObject parent, string name, string text, float fontSize)
    {
        GameObject textGO = new GameObject(name);
        textGO.transform.SetParent(parent.transform, false);

        // 添加RectTransform组件
        RectTransform rect = textGO.AddComponent<RectTransform>();

        TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
        textComponent.text = text;
        textComponent.fontSize = fontSize;
        textComponent.color = textColor;
        textComponent.alignment = TextAlignmentOptions.Center;

        rect.sizeDelta = new Vector2(0, fontSize + 10);

        return textGO;
    }
    
    /// <summary>
    /// 创建AudioRecorder
    /// </summary>
    private GameObject CreateAudioRecorder()
    {
        GameObject recorderGO = GameObject.Find("AudioRecorder");
        if (recorderGO == null)
        {
            recorderGO = new GameObject("AudioRecorder");
            recorderGO.AddComponent<AudioRecorder>();
        }
        return recorderGO;
    }
    
    /// <summary>
    /// 设置RecordSceneUI
    /// </summary>
    private void SetupRecordSceneUI(GameObject canvasGO)
    {
        GameObject uiControllerGO = GameObject.Find("RecordSceneUI");
        if (uiControllerGO == null)
        {
            uiControllerGO = new GameObject("RecordSceneUI");
        }
        
        RecordSceneUI uiController = uiControllerGO.GetComponent<RecordSceneUI>();
        if (uiController == null)
        {
            uiController = uiControllerGO.AddComponent<RecordSceneUI>();
        }
        
        // 通过反射设置UI引用（在实际使用中，这些引用需要在Inspector中手动设置）
        Debug.Log("请在Inspector中为RecordSceneUI组件设置UI引用：");
        Debug.Log("- Record Button: Canvas/ButtonArea/RecordButton");
        Debug.Log("- Play Button: Canvas/ButtonArea/PlayButton");
        Debug.Log("- Save Button: Canvas/ButtonArea/SaveButton");
        Debug.Log("- Clear Button: Canvas/ButtonArea/ClearButton");
        Debug.Log("- Status Text: Canvas/StatusArea/StatusText");
        Debug.Log("- Time Text: Canvas/StatusArea/TimeText");
        Debug.Log("- Instruction Text: Canvas/StatusArea/InstructionText");
        Debug.Log("- Audio Recorder: AudioRecorder");
    }
}
