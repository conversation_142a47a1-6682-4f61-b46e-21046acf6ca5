using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using VLB;

public class LightController : MonoBehaviour
{
    private VolumetricLightBeamHD _lightBeam;
    private void Awake()
    {
        _lightBeam = GetComponent<VolumetricLightBeamHD>();
        
        EventBetter.Listen(this, (SpotlightHueEvent she) =>
        {
            _lightBeam.colorFlat = she.HueColor;
        });
    }
    
   
}
