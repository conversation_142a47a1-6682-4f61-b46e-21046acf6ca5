using UnityEngine;
using System.Collections;

/// <summary>
/// AudioRecorder使用示例
/// 展示如何在代码中使用AudioRecorder组件
/// </summary>
public class AudioRecorderExample : MonoBehaviour
{
    [Header("Audio Recorder")]
    [SerializeField] private AudioRecorder audioRecorder;
    
    [Header("Example Settings")]
    [SerializeField] private bool autoStartExample = false;
    [SerializeField] private float exampleRecordTime = 5f;
    
    private void Start()
    {
        // 如果没有指定AudioRecorder，尝试找到它
        if (audioRecorder == null)
        {
            audioRecorder = FindObjectOfType<AudioRecorder>();
            if (audioRecorder == null)
            {
                Debug.LogError("未找到AudioRecorder组件！");
                return;
            }
        }
        
        // 订阅事件
        SubscribeToEvents();
        
        // 如果启用自动示例，开始演示
        if (autoStartExample)
        {
            StartCoroutine(AutoRecordingExample());
        }
    }
    
    /// <summary>
    /// 订阅AudioRecorder事件
    /// </summary>
    private void SubscribeToEvents()
    {
        audioRecorder.OnRecordingStarted += OnRecordingStarted;
        audioRecorder.OnRecordingStopped += OnRecordingStopped;
        audioRecorder.OnRecordingSaved += OnRecordingSaved;
        audioRecorder.OnError += OnError;
    }
    
    /// <summary>
    /// 取消订阅事件
    /// </summary>
    private void UnsubscribeFromEvents()
    {
        if (audioRecorder != null)
        {
            audioRecorder.OnRecordingStarted -= OnRecordingStarted;
            audioRecorder.OnRecordingStopped -= OnRecordingStopped;
            audioRecorder.OnRecordingSaved -= OnRecordingSaved;
            audioRecorder.OnError -= OnError;
        }
    }
    
    /// <summary>
    /// 自动录音示例协程
    /// </summary>
    private IEnumerator AutoRecordingExample()
    {
        Debug.Log("=== 开始自动录音示例 ===");
        
        // 等待1秒
        yield return new WaitForSeconds(1f);
        
        // 开始录音
        Debug.Log("开始录音...");
        audioRecorder.StartRecording();
        
        // 录音指定时间
        yield return new WaitForSeconds(exampleRecordTime);
        
        // 停止录音
        Debug.Log("停止录音...");
        audioRecorder.StopRecording();
        
        // 等待1秒
        yield return new WaitForSeconds(1f);
        
        // 播放录音
        Debug.Log("播放录音...");
        audioRecorder.PlayRecording();
        
        // 等待播放完成
        yield return new WaitForSeconds(exampleRecordTime + 1f);
        
        // 保存录音
        Debug.Log("保存录音...");
        audioRecorder.SaveRecording();
        
        Debug.Log("=== 自动录音示例完成 ===");
    }
    
    /// <summary>
    /// 手动开始录音示例
    /// </summary>
    [ContextMenu("Start Manual Recording Example")]
    public void StartManualRecordingExample()
    {
        StartCoroutine(ManualRecordingExample());
    }
    
    /// <summary>
    /// 手动录音示例协程
    /// </summary>
    private IEnumerator ManualRecordingExample()
    {
        Debug.Log("=== 手动录音示例 ===");
        Debug.Log("请在5秒内说话...");
        
        // 开始录音
        audioRecorder.StartRecording();
        
        // 等待5秒
        yield return new WaitForSeconds(5f);
        
        // 停止录音
        audioRecorder.StopRecording();
        
        Debug.Log("录音完成，2秒后播放...");
        yield return new WaitForSeconds(2f);
        
        // 播放录音
        audioRecorder.PlayRecording();
    }
    
    /// <summary>
    /// 测试所有功能
    /// </summary>
    [ContextMenu("Test All Functions")]
    public void TestAllFunctions()
    {
        StartCoroutine(TestAllFunctionsCoroutine());
    }
    
    /// <summary>
    /// 测试所有功能协程
    /// </summary>
    private IEnumerator TestAllFunctionsCoroutine()
    {
        Debug.Log("=== 测试所有功能 ===");
        
        // 测试录音
        Debug.Log("1. 测试录音功能...");
        audioRecorder.StartRecording();
        yield return new WaitForSeconds(3f);
        audioRecorder.StopRecording();
        
        // 测试播放
        Debug.Log("2. 测试播放功能...");
        yield return new WaitForSeconds(1f);
        audioRecorder.PlayRecording();
        yield return new WaitForSeconds(4f);
        
        // 测试保存
        Debug.Log("3. 测试保存功能...");
        audioRecorder.SaveRecording();
        yield return new WaitForSeconds(1f);
        
        // 测试清除
        Debug.Log("4. 测试清除功能...");
        audioRecorder.ClearRecording();
        
        Debug.Log("=== 所有功能测试完成 ===");
    }
    
    // 事件处理方法
    private void OnRecordingStarted()
    {
        Debug.Log("✓ 录音已开始");
    }
    
    private void OnRecordingStopped()
    {
        Debug.Log("✓ 录音已停止");
    }
    
    private void OnRecordingSaved(string filePath)
    {
        Debug.Log($"✓ 录音已保存到: {filePath}");
    }
    
    private void OnError(string errorMessage)
    {
        Debug.LogError($"✗ 录音错误: {errorMessage}");
    }
    
    /// <summary>
    /// 获取录音状态信息
    /// </summary>
    public void LogRecordingStatus()
    {
        if (audioRecorder == null)
        {
            Debug.Log("AudioRecorder组件未找到");
            return;
        }
        
        Debug.Log("=== 录音状态信息 ===");
        Debug.Log($"正在录音: {audioRecorder.IsRecording}");
        Debug.Log($"有录音数据: {audioRecorder.HasRecording}");
        Debug.Log($"录音时长: {audioRecorder.GetRecordingTime():F2}秒");
        Debug.Log($"保存路径: {Application.persistentDataPath}");
    }
    
    private void Update()
    {
        // 快捷键测试
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestAllFunctions();
        }
        
        if (Input.GetKeyDown(KeyCode.I))
        {
            LogRecordingStatus();
        }
        
        if (Input.GetKeyDown(KeyCode.M))
        {
            StartManualRecordingExample();
        }
    }
    
    private void OnDestroy()
    {
        UnsubscribeFromEvents();
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 300));
        GUILayout.Label("=== AudioRecorder 示例 ===");
        
        if (audioRecorder != null)
        {
            GUILayout.Label($"状态: {(audioRecorder.IsRecording ? "录音中" : "空闲")}");
            GUILayout.Label($"有录音: {(audioRecorder.HasRecording ? "是" : "否")}");
            GUILayout.Label($"时长: {audioRecorder.GetRecordingTime():F1}s");
        }
        
        GUILayout.Space(10);
        GUILayout.Label("测试按钮:");
        
        if (GUILayout.Button("测试所有功能 (T)"))
        {
            TestAllFunctions();
        }
        
        if (GUILayout.Button("手动录音示例 (M)"))
        {
            StartManualRecordingExample();
        }
        
        if (GUILayout.Button("显示状态信息 (I)"))
        {
            LogRecordingStatus();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("快捷键:");
        GUILayout.Label("T - 测试所有功能");
        GUILayout.Label("M - 手动录音示例");
        GUILayout.Label("I - 显示状态信息");
        
        GUILayout.EndArea();
    }
}
