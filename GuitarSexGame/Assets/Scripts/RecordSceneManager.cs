using UnityEngine;
using UnityEngine.SceneManagement;

/// <summary>
/// 录音场景管理器 - 提供场景切换和基本管理功能
/// </summary>
public class RecordSceneManager : MonoBehaviour
{
    [Header("Scene Management")]
    [SerializeField] private string mainSceneName = "MainScene";
    [SerializeField] private KeyCode exitKey = KeyCode.Escape;
    [SerializeField] private bool showDebugInfo = true;
    
    [Header("Audio Recorder Reference")]
    [SerializeField] private AudioRecorder audioRecorder;
    
    private void Start()
    {
        // 如果没有指定AudioRecorder，尝试找到它
        if (audioRecorder == null)
        {
            audioRecorder = FindObjectOfType<AudioRecorder>();
        }
        
        if (showDebugInfo)
        {
            Debug.Log("录音场景已加载");
            Debug.Log($"按 {exitKey} 键退出场景");
            Debug.Log("录音文件保存路径: " + Application.persistentDataPath);
        }
    }
    
    private void Update()
    {
        // 处理退出键
        if (Input.GetKeyDown(exitKey))
        {
            ExitScene();
        }
        
        // 处理快捷键
        HandleShortcuts();
    }
    
    /// <summary>
    /// 处理快捷键
    /// </summary>
    private void HandleShortcuts()
    {
        if (audioRecorder == null) return;
        
        // 空格键：开始/停止录音
        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (audioRecorder.IsRecording)
            {
                audioRecorder.StopRecording();
            }
            else
            {
                audioRecorder.StartRecording();
            }
        }
        
        // P键：播放录音
        if (Input.GetKeyDown(KeyCode.P))
        {
            audioRecorder.PlayRecording();
        }
        
        // S键：保存录音
        if (Input.GetKeyDown(KeyCode.S))
        {
            audioRecorder.SaveRecording();
        }
        
        // C键：清除录音
        if (Input.GetKeyDown(KeyCode.C))
        {
            audioRecorder.ClearRecording();
        }
    }
    
    /// <summary>
    /// 退出场景
    /// </summary>
    public void ExitScene()
    {
        if (audioRecorder != null && audioRecorder.IsRecording)
        {
            audioRecorder.StopRecording();
        }
        
        // 尝试加载主场景，如果不存在则退出应用
        if (Application.isEditor)
        {
            Debug.Log("在编辑器中，停止播放");
            #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
            #endif
        }
        else
        {
            // 尝试加载主场景
            try
            {
                SceneManager.LoadScene(mainSceneName);
            }
            catch
            {
                // 如果主场景不存在，退出应用
                Application.Quit();
            }
        }
    }
    
    /// <summary>
    /// 打开录音文件夹
    /// </summary>
    public void OpenRecordingFolder()
    {
        string path = Application.persistentDataPath;
        
        #if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        System.Diagnostics.Process.Start("explorer.exe", path);
        #elif UNITY_EDITOR_OSX || UNITY_STANDALONE_OSX
        System.Diagnostics.Process.Start("open", path);
        #elif UNITY_EDITOR_LINUX || UNITY_STANDALONE_LINUX
        System.Diagnostics.Process.Start("xdg-open", path);
        #endif
        
        Debug.Log($"录音文件夹路径: {path}");
    }
    
    /// <summary>
    /// 显示帮助信息
    /// </summary>
    public void ShowHelp()
    {
        string helpText = @"
=== 录音场景帮助 ===

快捷键：
- 空格键：开始/停止录音
- P键：播放录音
- S键：保存录音
- C键：清除录音
- ESC键：退出场景

按钮操作：
- 开始录音：开始录制音频
- 停止录音：停止录制
- 播放录音：播放已录制的音频
- 保存录音：将录音保存为WAV文件
- 清除录音：删除当前录音

文件保存：
- 录音文件保存在：" + Application.persistentDataPath + @"
- 文件格式：WAV
- 文件命名：Recording_日期_时间.wav

注意事项：
- 确保麦克风已连接并有权限
- 录音时长最大60秒
- 文件会自动保存到设备存储
";
        
        Debug.Log(helpText);
    }
    
    private void OnGUI()
    {
        if (!showDebugInfo) return;
        
        // 显示调试信息
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== 录音场景调试信息 ===");
        
        if (audioRecorder != null)
        {
            GUILayout.Label($"录音状态: {(audioRecorder.IsRecording ? "录音中" : "停止")}");
            GUILayout.Label($"有录音: {(audioRecorder.HasRecording ? "是" : "否")}");
            GUILayout.Label($"录音时长: {audioRecorder.GetRecordingTime():F1}秒");
        }
        else
        {
            GUILayout.Label("未找到AudioRecorder组件");
        }
        
        GUILayout.Space(10);
        GUILayout.Label("快捷键:");
        GUILayout.Label("空格 - 录音  P - 播放  S - 保存  C - 清除");
        GUILayout.Label($"{exitKey} - 退出");
        
        GUILayout.Space(10);
        if (GUILayout.Button("打开录音文件夹"))
        {
            OpenRecordingFolder();
        }
        
        if (GUILayout.Button("显示帮助"))
        {
            ShowHelp();
        }
        
        GUILayout.EndArea();
    }
}
