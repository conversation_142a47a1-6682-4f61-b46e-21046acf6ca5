using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using VLB;

public enum FlickerType
{
    OnNOff,
    OnOnOnOffOffOff,
    SimpleOnOff
}

public class FlickerController : MonoBehaviour
{
    public GameObject[] flickerGroupA, flickerGroupB;
    public float flickerInterval;

    private float _flickerTimer;
    private FlickerType _currentFlickerType;

    [SerializeField] private bool _flickering = false;
    private VolumetricLightBeamHD[] _lightBeams;
    private bool _flickered = false;

    private void Awake()
    {
        _lightBeams = GetComponentsInChildren<VolumetricLightBeamHD>();

        EventBetter.Listen(this, (FlickerEvent fe) =>
        {
            flickerInterval = fe.FlickerInterval;
            _currentFlickerType = fe.CurrentFlickerType;
            _flickering = true;
        });

        EventBetter.Listen(this, (StopFlickerEvent sfe) => { _flickering = false; });
        
        EventBetter.Listen(this, (SensitivityLevelChangeEvent slce) =>
        {
            ResetFlicker();
        });
        
        EventBetter.Listen(this, (MMInputs mminputs) =>
        {
            switch (_currentFlickerType)
            {
                case FlickerType.OnNOff:
                {
                    foreach (var o in flickerGroupA)
                    {
                        o.gameObject.SetActive(_flickered);
                    }

                    foreach (var o in flickerGroupB)
                    {
                        o.gameObject.SetActive(!_flickered);
                    }

                    _flickered = !_flickered;
                }
                    break;
                case FlickerType.OnOnOnOffOffOff:
                    for (int i = 0; i < _lightBeams.Length; i++)
                    {
                        _lightBeams[i].gameObject.SetActive(!_flickered);
                    }
                    _flickered = !_flickered;
                    break;
                case FlickerType.SimpleOnOff:
                    foreach (var o in flickerGroupA)
                    {
                        o.gameObject.SetActive(_flickered);
                    }
                    _flickered = !_flickered;
                    break;
            }
        });
    }

    private void Update()
    {
        // if (_flickering)
        // {
        //     if (_flickerTimer <= 0)
        //     {
        //         _flickerTimer = flickerInterval;
        //         switch (_currentFlickerType)
        //         {
        //             case FlickerType.OnNOff:
        //             {
        //                 foreach (var o in flickerGroupA)
        //                 {
        //                     o.gameObject.SetActive(_flickered);
        //                 }
        //
        //                 foreach (var o in flickerGroupB)
        //                 {
        //                     o.gameObject.SetActive(!_flickered);
        //                 }
        //
        //                 _flickered = !_flickered;
        //             }
        //                 break;
        //             case FlickerType.OnOnOnOffOffOff:
        //                 for (int i = 0; i < _lightBeams.Length; i++)
        //                 {
        //                     _lightBeams[i].gameObject.SetActive(!_flickered);
        //                 }
        //                 _flickered = !_flickered;
        //                 break;
        //         }
        //     }
        //     else
        //     {
        //         _flickerTimer -= Time.deltaTime;
        //     }
        // }
        // else
        // {
        //     ResetFlicker();
        // }
    }

    private void ResetFlicker()
    {
        foreach (var o in flickerGroupA)
        {
            o.gameObject.SetActive(true);
        }

        foreach (var o in flickerGroupB)
        {
            o.gameObject.SetActive(false);
        }

        _flickerTimer = 0;
        _flickered = false;
    }
}