using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 录音场景的UI控制器
/// </summary>
public class RecordSceneUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Button recordButton;
    [SerializeField] private Button playButton;
    [SerializeField] private Button saveButton;
    [SerializeField] private Button clearButton;
    [SerializeField] private TextMeshProUGUI statusText;
    [SerializeField] private TextMeshProUGUI timeText;
    [SerializeField] private TextMeshProUGUI instructionText;
    
    [Header("Audio Recorder")]
    [SerializeField] private AudioRecorder audioRecorder;
    
    [Header("UI Settings")]
    [SerializeField] private Color recordingColor = Color.red;
    [SerializeField] private Color normalColor = Color.white;
    
    // UI状态
    private bool isInitialized = false;
    
    private void Start()
    {
        InitializeUI();
        SetupEventListeners();
        UpdateUI();
    }
    
    /// <summary>
    /// 初始化UI组件
    /// </summary>
    private void InitializeUI()
    {
        // 如果没有指定AudioRecorder，尝试在场景中找到
        if (audioRecorder == null)
        {
            audioRecorder = FindObjectOfType<AudioRecorder>();
            if (audioRecorder == null)
            {
                Debug.LogError("未找到AudioRecorder组件！");
                return;
            }
        }
        
        // 设置初始文本
        if (instructionText != null)
        {
            instructionText.text = "点击录音按钮开始录制音频\n录音完成后可以播放、保存或清除录音";
        }
        
        isInitialized = true;
    }
    
    /// <summary>
    /// 设置事件监听器
    /// </summary>
    private void SetupEventListeners()
    {
        if (!isInitialized) return;
        
        // 按钮事件
        if (recordButton != null)
        {
            recordButton.onClick.AddListener(OnRecordButtonClicked);
        }
        
        if (playButton != null)
        {
            playButton.onClick.AddListener(OnPlayButtonClicked);
        }
        
        if (saveButton != null)
        {
            saveButton.onClick.AddListener(OnSaveButtonClicked);
        }
        
        if (clearButton != null)
        {
            clearButton.onClick.AddListener(OnClearButtonClicked);
        }
        
        // AudioRecorder事件
        audioRecorder.OnRecordingStarted += OnRecordingStarted;
        audioRecorder.OnRecordingStopped += OnRecordingStopped;
        audioRecorder.OnRecordingSaved += OnRecordingSaved;
        audioRecorder.OnError += OnError;
    }
    
    /// <summary>
    /// 录音按钮点击事件
    /// </summary>
    private void OnRecordButtonClicked()
    {
        if (audioRecorder.IsRecording)
        {
            audioRecorder.StopRecording();
        }
        else
        {
            audioRecorder.StartRecording();
        }
    }
    
    /// <summary>
    /// 播放按钮点击事件
    /// </summary>
    private void OnPlayButtonClicked()
    {
        audioRecorder.PlayRecording();
    }
    
    /// <summary>
    /// 保存按钮点击事件
    /// </summary>
    private void OnSaveButtonClicked()
    {
        audioRecorder.SaveRecording();
    }
    
    /// <summary>
    /// 清除按钮点击事件
    /// </summary>
    private void OnClearButtonClicked()
    {
        audioRecorder.ClearRecording();
        UpdateUI();
    }
    
    /// <summary>
    /// 录音开始事件处理
    /// </summary>
    private void OnRecordingStarted()
    {
        UpdateUI();
    }
    
    /// <summary>
    /// 录音停止事件处理
    /// </summary>
    private void OnRecordingStopped()
    {
        UpdateUI();
    }
    
    /// <summary>
    /// 录音保存事件处理
    /// </summary>
    private void OnRecordingSaved(string filePath)
    {
        if (statusText != null)
        {
            statusText.text = $"录音已保存到:\n{filePath}";
            statusText.color = Color.green;
        }
        
        // 3秒后恢复正常状态显示
        Invoke(nameof(UpdateUI), 3f);
    }
    
    /// <summary>
    /// 错误事件处理
    /// </summary>
    private void OnError(string errorMessage)
    {
        if (statusText != null)
        {
            statusText.text = $"错误: {errorMessage}";
            statusText.color = Color.red;
        }
        
        // 3秒后恢复正常状态显示
        Invoke(nameof(UpdateUI), 3f);
    }
    
    /// <summary>
    /// 更新UI状态
    /// </summary>
    private void UpdateUI()
    {
        if (!isInitialized) return;
        
        // 更新录音按钮
        if (recordButton != null)
        {
            var buttonText = recordButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = audioRecorder.IsRecording ? "停止录音" : "开始录音";
            }
            
            var buttonImage = recordButton.GetComponent<Image>();
            if (buttonImage != null)
            {
                buttonImage.color = audioRecorder.IsRecording ? recordingColor : normalColor;
            }
        }
        
        // 更新其他按钮的可用状态
        if (playButton != null)
        {
            playButton.interactable = audioRecorder.HasRecording && !audioRecorder.IsRecording;
        }
        
        if (saveButton != null)
        {
            saveButton.interactable = audioRecorder.HasRecording && !audioRecorder.IsRecording;
        }
        
        if (clearButton != null)
        {
            clearButton.interactable = audioRecorder.HasRecording || audioRecorder.IsRecording;
        }
        
        // 更新状态文本
        if (statusText != null)
        {
            if (audioRecorder.IsRecording)
            {
                statusText.text = "正在录音...";
                statusText.color = recordingColor;
            }
            else if (audioRecorder.HasRecording)
            {
                statusText.text = "录音完成，可以播放或保存";
                statusText.color = Color.green;
            }
            else
            {
                statusText.text = "准备录音";
                statusText.color = normalColor;
            }
        }
    }
    
    private void Update()
    {
        if (!isInitialized) return;
        
        // 更新时间显示
        if (timeText != null)
        {
            float recordingTime = audioRecorder.GetRecordingTime();
            if (recordingTime > 0)
            {
                int minutes = Mathf.FloorToInt(recordingTime / 60);
                int seconds = Mathf.FloorToInt(recordingTime % 60);
                timeText.text = $"时长: {minutes:00}:{seconds:00}";
            }
            else
            {
                timeText.text = "时长: 00:00";
            }
        }
    }
    
    private void OnDestroy()
    {
        // 清理事件监听器
        if (audioRecorder != null)
        {
            audioRecorder.OnRecordingStarted -= OnRecordingStarted;
            audioRecorder.OnRecordingStopped -= OnRecordingStopped;
            audioRecorder.OnRecordingSaved -= OnRecordingSaved;
            audioRecorder.OnError -= OnError;
        }
        
        // 清理按钮事件
        if (recordButton != null)
        {
            recordButton.onClick.RemoveAllListeners();
        }
        
        if (playButton != null)
        {
            playButton.onClick.RemoveAllListeners();
        }
        
        if (saveButton != null)
        {
            saveButton.onClick.RemoveAllListeners();
        }
        
        if (clearButton != null)
        {
            clearButton.onClick.RemoveAllListeners();
        }
    }
}
