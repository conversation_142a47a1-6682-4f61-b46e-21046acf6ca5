using UnityEngine;

/// <summary>
/// 简单的录音测试脚本 - 不依赖UI，直接测试AudioRecorder功能
/// </summary>
public class SimpleRecordTest : MonoBehaviour
{
    [Header("Audio Recorder")]
    [SerializeField] private AudioRecorder audioRecorder;
    
    [Header("Test Settings")]
    [SerializeField] private bool showInstructions = true;
    
    private void Start()
    {
        // 如果没有指定AudioRecorder，创建一个
        if (audioRecorder == null)
        {
            GameObject recorderGO = new GameObject("AudioRecorder");
            audioRecorder = recorderGO.AddComponent<AudioRecorder>();
        }
        
        // 订阅事件
        audioRecorder.OnRecordingStarted += () => Debug.Log("✓ Recording started");
        audioRecorder.OnRecordingStopped += () => Debug.Log("✓ Recording stopped");
        audioRecorder.OnRecordingSaved += (path) => Debug.Log($"✓ Recording saved: {path}");
        audioRecorder.OnError += (error) => Debug.LogError($"✗ Error: {error}");
        
        if (showInstructions)
        {
            Debug.Log("=== Simple Recording Test ===");
            Debug.Log("Space - Start/Stop recording");
            Debug.Log("S - Save recording");
            Debug.Log("I - Show status info");
        }
    }
    
    private void Update()
    {
        // 空格键：开始/停止录音
        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (audioRecorder.IsRecording)
            {
                audioRecorder.StopRecording();
            }
            else
            {
                audioRecorder.StartRecording();
            }
        }
        
        // S键：保存录音
        if (Input.GetKeyDown(KeyCode.S))
        {
            audioRecorder.SaveRecording();
        }

        // I键：显示状态信息
        if (Input.GetKeyDown(KeyCode.I))
        {
            ShowStatus();
        }
    }
    
    private void ShowStatus()
    {
        Debug.Log("=== Recording Status ===");
        Debug.Log($"Is Recording: {audioRecorder.IsRecording}");
        Debug.Log($"Has Recording: {audioRecorder.HasRecording}");
        Debug.Log($"Recording Duration: {audioRecorder.GetRecordingTime():F2} seconds");
        Debug.Log($"Save Path: {Application.persistentDataPath}");
    }
    
    private void OnGUI()
    {
        if (!showInstructions) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label("=== Simple Recording Test ===");

        GUILayout.Label($"Recording Status: {(audioRecorder.IsRecording ? "Recording" : "Stopped")}");
        GUILayout.Label($"Has Recording: {(audioRecorder.HasRecording ? "Yes" : "No")}");
        GUILayout.Label($"Duration: {audioRecorder.GetRecordingTime():F1}s");

        GUILayout.Space(10);
        GUILayout.Label("Controls:");

        if (GUILayout.Button(audioRecorder.IsRecording ? "Stop Recording (Space)" : "Start Recording (Space)"))
        {
            if (audioRecorder.IsRecording)
            {
                audioRecorder.StopRecording();
            }
            else
            {
                audioRecorder.StartRecording();
            }
        }

        GUI.enabled = audioRecorder.HasRecording && !audioRecorder.IsRecording;
        if (GUILayout.Button("Save Recording (S)"))
        {
            audioRecorder.SaveRecording();
        }
        GUI.enabled = true;

        if (GUILayout.Button("Show Status (I)"))
        {
            ShowStatus();
        }
        
        GUILayout.EndArea();
    }
}
