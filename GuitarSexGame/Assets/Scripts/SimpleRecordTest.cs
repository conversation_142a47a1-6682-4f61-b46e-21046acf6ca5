using UnityEngine;

/// <summary>
/// 简单的录音测试脚本 - 不依赖UI，直接测试AudioRecorder功能
/// </summary>
public class SimpleRecordTest : MonoBehaviour
{
    [Header("Audio Recorder")]
    [SerializeField] private AudioRecorder audioRecorder;
    
    [Header("Test Settings")]
    [SerializeField] private bool showInstructions = true;
    
    private void Start()
    {
        // 如果没有指定AudioRecorder，创建一个
        if (audioRecorder == null)
        {
            GameObject recorderGO = new GameObject("AudioRecorder");
            audioRecorder = recorderGO.AddComponent<AudioRecorder>();
        }
        
        // 订阅事件
        audioRecorder.OnRecordingStarted += () => Debug.Log("✓ 录音开始");
        audioRecorder.OnRecordingStopped += () => Debug.Log("✓ 录音停止");
        audioRecorder.OnRecordingSaved += (path) => Debug.Log($"✓ 录音已保存: {path}");
        audioRecorder.OnError += (error) => Debug.LogError($"✗ 错误: {error}");
        
        if (showInstructions)
        {
            Debug.Log("=== 简单录音测试 ===");
            Debug.Log("空格键 - 开始/停止录音");
            Debug.Log("P键 - 播放录音");
            Debug.Log("S键 - 保存录音");
            Debug.Log("C键 - 清除录音");
            Debug.Log("I键 - 显示状态信息");
        }
    }
    
    private void Update()
    {
        // 空格键：开始/停止录音
        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (audioRecorder.IsRecording)
            {
                audioRecorder.StopRecording();
            }
            else
            {
                audioRecorder.StartRecording();
            }
        }
        
        // P键：播放录音
        if (Input.GetKeyDown(KeyCode.P))
        {
            audioRecorder.PlayRecording();
        }
        
        // S键：保存录音
        if (Input.GetKeyDown(KeyCode.S))
        {
            audioRecorder.SaveRecording();
        }
        
        // C键：清除录音
        if (Input.GetKeyDown(KeyCode.C))
        {
            audioRecorder.ClearRecording();
        }
        
        // I键：显示状态信息
        if (Input.GetKeyDown(KeyCode.I))
        {
            ShowStatus();
        }
    }
    
    private void ShowStatus()
    {
        Debug.Log("=== 录音状态 ===");
        Debug.Log($"正在录音: {audioRecorder.IsRecording}");
        Debug.Log($"有录音数据: {audioRecorder.HasRecording}");
        Debug.Log($"录音时长: {audioRecorder.GetRecordingTime():F2}秒");
        Debug.Log($"保存路径: {Application.persistentDataPath}");
    }
    
    private void OnGUI()
    {
        if (!showInstructions) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== 简单录音测试 ===");
        
        GUILayout.Label($"录音状态: {(audioRecorder.IsRecording ? "录音中" : "停止")}");
        GUILayout.Label($"有录音: {(audioRecorder.HasRecording ? "是" : "否")}");
        GUILayout.Label($"录音时长: {audioRecorder.GetRecordingTime():F1}秒");
        
        GUILayout.Space(10);
        GUILayout.Label("操作:");
        
        if (GUILayout.Button(audioRecorder.IsRecording ? "停止录音 (空格)" : "开始录音 (空格)"))
        {
            if (audioRecorder.IsRecording)
            {
                audioRecorder.StopRecording();
            }
            else
            {
                audioRecorder.StartRecording();
            }
        }
        
        GUI.enabled = audioRecorder.HasRecording && !audioRecorder.IsRecording;
        if (GUILayout.Button("播放录音 (P)"))
        {
            audioRecorder.PlayRecording();
        }
        
        if (GUILayout.Button("保存录音 (S)"))
        {
            audioRecorder.SaveRecording();
        }
        GUI.enabled = true;
        
        GUI.enabled = audioRecorder.HasRecording || audioRecorder.IsRecording;
        if (GUILayout.Button("清除录音 (C)"))
        {
            audioRecorder.ClearRecording();
        }
        GUI.enabled = true;
        
        if (GUILayout.Button("显示状态 (I)"))
        {
            ShowStatus();
        }
        
        GUILayout.EndArea();
    }
}
