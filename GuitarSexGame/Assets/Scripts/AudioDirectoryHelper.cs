using UnityEngine;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// 帮助创建和管理Audio目录的工具类
/// </summary>
public class AudioDirectoryHelper : MonoBehaviour
{
    [Header("Directory Settings")]
    [SerializeField] private bool createOnStart = true;
    [SerializeField] private bool showPathInConsole = true;
    
    private void Start()
    {
        if (createOnStart)
        {
            CreateAudioDirectory();
        }
    }
    
    /// <summary>
    /// 创建Audio目录
    /// </summary>
    [ContextMenu("Create Audio Directory")]
    public void CreateAudioDirectory()
    {
        string audioDir = Path.Combine(Application.dataPath, "Audio");
        
        if (!Directory.Exists(audioDir))
        {
            Directory.CreateDirectory(audioDir);
            Debug.Log($"Created Audio directory at: {audioDir}");
            
            // 创建一个.gitkeep文件以确保目录在版本控制中被跟踪
            string gitkeepPath = Path.Combine(audioDir, ".gitkeep");
            File.WriteAllText(gitkeepPath, "");
            
            #if UNITY_EDITOR
            // 刷新Asset数据库以在Unity编辑器中显示新目录
            AssetDatabase.Refresh();
            #endif
        }
        else
        {
            if (showPathInConsole)
            {
                Debug.Log($"Audio directory already exists at: {audioDir}");
            }
        }
    }
    
    /// <summary>
    /// 获取Audio目录路径
    /// </summary>
    public string GetAudioDirectoryPath()
    {
        return Path.Combine(Application.dataPath, "Audio");
    }
    
    /// <summary>
    /// 检查Audio目录是否存在
    /// </summary>
    public bool AudioDirectoryExists()
    {
        return Directory.Exists(GetAudioDirectoryPath());
    }
    
    /// <summary>
    /// 打开Audio目录
    /// </summary>
    [ContextMenu("Open Audio Directory")]
    public void OpenAudioDirectory()
    {
        string audioDir = GetAudioDirectoryPath();
        
        if (!Directory.Exists(audioDir))
        {
            CreateAudioDirectory();
        }
        
        #if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        System.Diagnostics.Process.Start("explorer.exe", audioDir);
        #elif UNITY_EDITOR_OSX || UNITY_STANDALONE_OSX
        System.Diagnostics.Process.Start("open", audioDir);
        #elif UNITY_EDITOR_LINUX || UNITY_STANDALONE_LINUX
        System.Diagnostics.Process.Start("xdg-open", audioDir);
        #endif
        
        Debug.Log($"Opening Audio directory: {audioDir}");
    }
    
    /// <summary>
    /// 列出Audio目录中的所有WAV文件
    /// </summary>
    [ContextMenu("List Audio Files")]
    public void ListAudioFiles()
    {
        string audioDir = GetAudioDirectoryPath();
        
        if (!Directory.Exists(audioDir))
        {
            Debug.Log("Audio directory does not exist.");
            return;
        }
        
        string[] wavFiles = Directory.GetFiles(audioDir, "*.wav");
        
        if (wavFiles.Length == 0)
        {
            Debug.Log("No WAV files found in Audio directory.");
        }
        else
        {
            Debug.Log($"Found {wavFiles.Length} WAV files in Audio directory:");
            foreach (string file in wavFiles)
            {
                string fileName = Path.GetFileName(file);
                FileInfo fileInfo = new FileInfo(file);
                Debug.Log($"- {fileName} ({fileInfo.Length} bytes, {fileInfo.LastWriteTime})");
            }
        }
    }
    
    private void OnGUI()
    {
        if (!showPathInConsole) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 120));
        GUILayout.Label("=== Audio Directory Helper ===");
        
        string audioDir = GetAudioDirectoryPath();
        bool exists = AudioDirectoryExists();
        
        GUILayout.Label($"Directory exists: {(exists ? "Yes" : "No")}");
        GUILayout.Label($"Path: Assets/Audio");
        
        if (GUILayout.Button("Create Directory"))
        {
            CreateAudioDirectory();
        }
        
        if (GUILayout.Button("Open Directory"))
        {
            OpenAudioDirectory();
        }
        
        if (GUILayout.Button("List Audio Files"))
        {
            ListAudioFiles();
        }
        
        GUILayout.EndArea();
    }
}
