using System;
using UnityEngine;

public class PelvisController : MonoBehaviour
{
    public float upwardForce = 10f; // Amount of upward force to apply
    private Rigidbody rb; // Reference to the Rigidbody component

    private void Awake()
    {
        EventBetter.Listen(this, (AlienJumpEvent aje) => {ApplyUpwardForce();});
    }

    private void Start()
    {
        // Get the Rigidbody component attached to this GameObject
        rb = GetComponent<Rigidbody>();

        // Ensure the Rigidbody exists
        if (rb == null)
        {
            Debug.LogError("No Rigidbody found on this GameObject. Please add a Rigidbody component.");
        }
    }

    private void Update()
    {
        // Check for space key press to apply upward force
        if (Input.GetKeyDown(KeyCode.Space))
        {
            ApplyUpwardForce();
        }
    }

    public void ApplyUpwardForce()
    {
        // Ensure the Rigidbody exists before applying force
        if (rb != null)
        {
            rb.AddForce(Vector3.up * upwardForce, ForceMode.Impulse);
        }
    }
}