using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BGMaterialController : MonoBehaviour
{
    public float[] cellDensities; // Array of cell densities for each material
    public float[] speed; // Array of speeds
    public Color[] colors; // Array of colors for each material
    public Color [] emissionColors; // Array of emission colors for each material
    public Material material; // Reference to the material to be modified
    public int currentPhase;
    private float initialDensity; // Initial density of the material
    private float initialSpeed; // Initial speed of the material
    private Color initialColor; // Initial color of the material
    private Color initialEmissionColor; // Initial emission color of the material
    // Start is called before the first frame update
    private void Awake()
    {
        EventBetter.Listen(this, (SensitivityLevelChangeEvent slce) =>
        {
            currentPhase = (int)slce.CurrentSensitivityLevel;
        });
    }

    void Start()
    {
        if (material != null)
        {
            initialColor = material.GetColor("_Color_1"); // Get the initial color of the material
            initialDensity = material.GetFloat("_CellDensity"); // Get the initial density of the material
            initialSpeed = material.GetFloat("_Speed"); // Get the initial speed of the material
            initialEmissionColor = material.GetColor("_Color"); // Get the initial emission color of the material
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (material != null && currentPhase < cellDensities.Length)
        {
            float lerpFactor = Time.deltaTime; 
            float currentDensity = material.GetFloat("_CellDensity");
            float currentSpeed = material.GetFloat("_Speed");
            Color currentColor = material.GetColor("_Color_1");
            Color currentEmissionColor = material.GetColor("_Color");

            material.SetFloat("_CellDensity", Mathf.Lerp(currentDensity, cellDensities[currentPhase], lerpFactor)); // Lerp the density
            material.SetFloat("_Speed", Mathf.Lerp(currentSpeed, speed[currentPhase], lerpFactor)); // Lerp the speed

            if (currentPhase == 1)
            {
                // Apply rotation effect to _Color_1
                material.SetColor("_Color_1", RotateColorEffect(speed[currentPhase], colors[currentPhase]));
                material.SetColor("_Color", Color.Lerp(currentEmissionColor, emissionColors[currentPhase], lerpFactor)); // Lerp the emission color
            }
            else if (currentPhase == 2)
            {
                // Apply rotation effect to both _Color_1 and _Color
                material.SetColor("_Color_1", RotateColorEffect(speed[currentPhase], colors[currentPhase]));
                material.SetColor("_Color", RotateColorEffect(speed[currentPhase], emissionColors[currentPhase]));
            }
            else
            {
                // Default behavior for other phases
                material.SetColor("_Color_1", Color.Lerp(currentColor, colors[currentPhase], lerpFactor)); // Lerp the color
                material.SetColor("_Color", Color.Lerp(currentEmissionColor, emissionColors[currentPhase], lerpFactor)); // Lerp the emission color
            }
        }
    }

    void OnDestroy()
    {
        if (material != null)
        {
            material.SetFloat("_CellDensity", initialDensity); // Reset the density of the material
            material.SetFloat("_Speed", initialSpeed); // Reset the speed of the material
            material.SetColor("_Color_1", initialColor); // Reset the color of the material
            material.SetColor("_Color", initialEmissionColor); // Reset the emission color of the material
        }
    }

    public Color RotateColorEffect(float rotationSpeed, Color baseColor)
    {
        if (material != null)
        {
            float r = Mathf.PingPong(Time.time * rotationSpeed, 1.0f) * baseColor.r;
            float g = Mathf.PingPong(Time.time * rotationSpeed + 0.33f, 1.0f) * baseColor.g;
            float b = Mathf.PingPong(Time.time * rotationSpeed + 0.66f, 1.0f) * baseColor.b;
            return new Color(r, g, b, baseColor.a);
        }
        return baseColor; // Return the base color if material is null
    }
    
}
