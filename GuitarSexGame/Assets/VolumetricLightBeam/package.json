{"name": "com.saladgamer.volumetriclightbeam", "displayName": "Volumetric Light Beam", "version": "2.0.0", "unity": "2018.4", "description": "The simple and efficient volumetric lighting solution compatible with every platforms: Windows PC, Mac OS X, Linux, WebGL, iOS, Android, VR, AR, Consoles, Built-in/Legacy Render Pipeline, SRP (URP & HDRP)!", "documentationUrl": "http://saladgamer.com/vlb-doc/", "changelogUrl": "http://saladgamer.com/vlb-doc/changelog/", "author": {"name": "Tech Salad", "email": "<EMAIL>", "url": "http://saladgamer.com/vlb-doc/"}, "keywords": ["spot", "spotlight", "noise", "shaft", "VR", "Oculus", "Vive", "AR", "ARkit", "ARCore", "mobile", "ios", "iphone", "android", "volumetric", "volume", "density", "light", "lighting", "beam", "cone", "procedural", "dynamic", "rays", "godrays", "occlusion", "gpu instancing", "srp", "lwrp", "urp", "hdrp"], "dependencies": {}}