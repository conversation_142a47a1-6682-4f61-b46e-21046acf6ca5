using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace RootMotion
{

    public abstract class AnimationModifier : MonoBeh<PERSON>our
    {
        protected Animator animator;
        protected <PERSON> baker;

        public virtual void OnInitiate(<PERSON> baker, Animator animator)
        {
            this.baker = baker;
            this.animator = animator;
        }

        public virtual void OnStartClip(AnimationClip clip) { }

        public virtual void OnBakerUpdate(float normalizedTime) { }
    }
}
